// 简单的测试脚本来验证voice API
const axios = require('axios');

const API_BASE_URL = 'http://localhost:5008/api';

async function testVoiceAPI() {
  try {
    console.log('测试voice API...');
    console.log('请求URL:', `${API_BASE_URL}/podcast/voice_roles?lang=zh`);

    const response = await axios.get(`${API_BASE_URL}/podcast/voice_roles?lang=zh`);
    console.log('API响应状态:', response.status);
    console.log('API响应:', JSON.stringify(response.data, null, 2));

    // 检查是否是你提供的格式
    if (response.data && response.data.voices) {
      console.log(`\n找到 ${response.data.voices.length} 个voice选项:`);
      response.data.voices.forEach((voice, index) => {
        console.log(`${index + 1}. ${voice.desc} (${voice.key})`);
      });
    } else if (response.data && response.data.zh) {
      console.log('\n当前API返回的是转换后的格式');
    } else {
      console.log('\n未知的API响应格式');
    }
  } catch (error) {
    console.error('API调用失败:', error.message);
    if (error.response) {
      console.error('响应状态:', error.response.status);
      console.error('响应数据:', error.response.data);
    }
  }
}

testVoiceAPI();
