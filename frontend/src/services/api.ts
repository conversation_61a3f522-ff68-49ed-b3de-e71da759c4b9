import axios from 'axios';
import { DialogueItem, ApiResponse, VoiceRoleItem } from '../types';

// API基础URL
const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:5008/api';

// 创建axios实例
const apiClient = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

/**
 * 生成音频
 * @param jsonDialogue 对话JSON字符串
 * @param language 语言('en'或'zh')
 * @param voiceMap 角色-voice对象数组（可选），每项含 key/desc
 * @returns 包含音频数据的响应
 */
export const generateAudio = async (
  jsonDialogue: string,
  language: string,
  voiceMap?: VoiceRoleItem[]
): Promise<ApiResponse> => {
  try {
    const payload: any = {
      json_dialogue: jsonDialogue,
      language: language,
    };
    if (voiceMap) {
      payload.voice_map = JSON.stringify(voiceMap);
    }
    const response = await apiClient.post<ApiResponse>('/podcast/generate/audio', payload, {
      headers: {
        'Content-Type': 'application/json'
      }
    });
    return response.data;
  } catch (error) {
    if (axios.isAxiosError(error) && error.response) {
      throw new Error(error.response.data.error || 'Failed to generate audio');
    }
    throw new Error('Network error occurred');
  }
};

/**
 * 生成播客脚本
 * @param inputText 输入的文章文本
 * @param language 语言('en'或'zh')
 * @returns 包含脚本数据的响应
 */
export const generateScript = async (inputText: string, language: string): Promise<{script: DialogueItem[]}> => {
  try {
    const response = await apiClient.post('/podcast/generate/script', {
      input_text: inputText,
      language: language
    }, {
      headers: {
        'Content-Type': 'application/json'
      }
    });
    return response.data;
  } catch (error) {
    if (axios.isAxiosError(error) && error.response) {
      throw new Error(error.response.data.error || 'Failed to generate script');
    }
    throw new Error('Network error occurred');
  }
};

/**
 * 获取所有可用角色
 * @param lang 可选，指定语言（如 'zh' 或 'en'），只返回该语言数据
 * @returns 角色对象，结构为 { zh: [{ key, desc }], en: [{ key, desc }] } 或 { zh: [...] }/{ en: [...] }
 */
/**
 * 获取所有可用角色
 * @param lang 可选，指定语言（如 'zh' 或 'en'），只返回该语言数据
 * @returns 角色对象，结构为 { zh: [{ role, voices }], en: [{ role, voices }] }
 */
export const getVoiceRoles = async (
  lang?: string
): Promise<{
  zh: { role: string; voices: VoiceRoleItem[] }[];
  en?: { role: string; voices: VoiceRoleItem[] }[];
}> => {
  try {
    const url = `${API_BASE_URL}/podcast/voice_roles${lang ? `?lang=${encodeURIComponent(lang)}` : ''}`;
    const response = await axios.get(url);
    const convert = (arr: any[] = []) =>
      arr.map((item: any, idx: number) => ({
        role: item.role ?? item.key ?? item.id ?? String(idx),
        voices: [ { key: item.key ?? item.id ?? item.role, desc: item.desc ?? item.voice ?? '' } ]
      }));
    const data = response.data || {};
    if (lang === 'zh') {
      return { zh: convert(data.zh) };
    }
    if (lang === 'en') {
      return { zh: [], en: convert(data.en) };
    }
    return {
      zh: convert(data.zh),
      ...(data.en ? { en: convert(data.en) } : {})
    };
  } catch (error) {
    throw new Error('无法获取角色列表');
  }
};
// 获取脚本历史记录
export const getScriptHistory = async (): Promise<any[]> => {
  try {
    const response = await apiClient.get('/podcast/history/scripts');
    return response.data;
  } catch (error) {
    throw new Error('无法获取脚本历史记录');
  }
};

// 获取音频历史记录
export const getAudioHistory = async (): Promise<any[]> => {
  try {
    const response = await apiClient.get('/podcast/history/audios');
    return response.data;
  } catch (error) {
    throw new Error('无法获取音频历史记录');
  }
};
/**
 * 音频后期合成
 * @param id 音频ID
 * @returns 接口响应
 */
export const editAudio = async (id: number): Promise<any> => {
  try {
    const payload = { id };
    const response = await apiClient.post(
      '/podcast/edit/audio',
      payload,
      {
        headers: {
          'Content-Type': 'application/json'
        }
      }
    );
    return response.data;
  } catch (error) {
    if (axios.isAxiosError(error) && error.response) {
      throw new Error(error.response.data.error || '音频后期合成失败');
    }
    throw new Error('网络错误');
  }
};
// 获取TTS语音生成历史（分页）
export const getTTSHistory = async (page: number = 1, limit: number = 10): Promise<{
  data: {
    id: number;
    text: string;
    voice: string;
    created_at: string;
    audio_url: string;
  }[];
  total: number;
  page: number;
  limit: number;
}> => {
  try {
    const response = await apiClient.get('/tts/history', {
      params: { page, limit }
    });
    return response.data;
  } catch (error) {
    throw new Error('无法获取TTS语音生成历史');
  }
};

/**
 * 获取TTS可用voice列表，返回 VoiceRoleItem[]，每项含 key/desc。
 */
export const getTTSVoices = async (): Promise<VoiceRoleItem[]> => {
  try {
    const response = await apiClient.get('/tts/voices');
    // 假设返回 { voices: [{key, desc}] }
    return response.data.voices || [];
  } catch (error) {
    throw new Error('无法获取TTS语音列表');
  }
};

// 生成TTS音频（voice 参数为 voice key）
export const generateTTS = async (text: string, voiceKey: string): Promise<{ audio_url: string }> => {
  try {
    const response = await apiClient.post('/tts/generate', { text, voice: voiceKey });
    return response.data;
  } catch (error) {
    throw new Error('TTS音频生成失败');
  }
};