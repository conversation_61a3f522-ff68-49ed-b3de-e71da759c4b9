import React, { useEffect, useState } from 'react';
import { VoiceRoleItem } from './types';
import { getTTSVoices, generateTTS } from './services/api';

const TTSGenerator: React.FC = () => {
  const [voices, setVoices] = useState<VoiceRoleItem[]>([]);
  const [text, setText] = useState('');
  const [selectedVoice, setSelectedVoice] = useState('');
  const [loadingVoices, setLoadingVoices] = useState(false);
  const [generating, setGenerating] = useState(false);
  const [audioUrl, setAudioUrl] = useState('');
  const [error, setError] = useState('');

  useEffect(() => {
    setLoadingVoices(true);
    getTTSVoices()
      .then((voiceList) => {
        setVoices(voiceList);
        if (voiceList.length > 0) setSelectedVoice(voiceList[0].key);
      })
      .catch(() => setError('获取语音列表失败'))
      .finally(() => setLoadingVoices(false));
  }, []);

  const handleGenerate = async () => {
    setError('');
    setGenerating(true);
    setAudioUrl('');
    try {
      const res = await generateTTS(text, selectedVoice);
      setAudioUrl(res.audio_url);
    } catch (e: any) {
      setError(e.message || '生成失败');
    } finally {
      setGenerating(false);
    }
  };

  return (
    <div style={{ maxWidth: 500, margin: '40px auto', padding: 24, border: '1px solid #eee', borderRadius: 8 }}>
      <h2>TTS语音生成</h2>
      <div style={{ marginBottom: 16 }}>
        <textarea
          rows={4}
          style={{ width: '100%', resize: 'vertical' }}
          placeholder="请输入要合成的文本"
          value={text}
          onChange={e => setText(e.target.value)}
        />
      </div>
      <div style={{ marginBottom: 16 }}>
        <label>选择语音：</label>
        {loadingVoices ? (
          <span>加载中...</span>
        ) : (
          <select
            value={selectedVoice}
            onChange={e => setSelectedVoice(e.target.value)}
            style={{ minWidth: 120 }}
          >
            {voices.map(v => (
              <option key={v.key} value={v.key}>
                {v.desc || v.key}
              </option>
            ))}
          </select>
        )}
      </div>
      <button
        onClick={handleGenerate}
        disabled={!text || !selectedVoice || generating}
        style={{ padding: '6px 18px', fontSize: 16 }}
      >
        {generating ? '生成中...' : '生成'}
      </button>
      {error && (
        <div style={{ color: 'red', marginTop: 12 }}>{error}</div>
      )}
      {audioUrl && (
        <div style={{ marginTop: 24 }}>
          <audio controls src={audioUrl} style={{ width: '100%' }} />
        </div>
      )}
    </div>
  );
};

export default TTSGenerator;