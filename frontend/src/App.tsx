import React, { useState, useEffect } from 'react';
import { Container, Row, Col, Navbar, Nav } from 'react-bootstrap';
import PodcastGenerator from './PodcastGenerator';
import History from './History';
import TTSGenerator from './TTSGenerator';
import './App.css';

const App: React.FC = () => {
  // 0: 播客生成器, 1: 历史记录, 2: TTS生成
  const [selected, setSelected] = useState<number>(0);

  // 菜单项与路由映射
  const menuRouteMap = [
    { key: 0, path: '/', label: '播客生成' },
    { key: 1, path: '/history', label: '历史记录' },
    { key: 2, path: '/tts-generator', label: 'TTS生成' }
  ];

  // 初始化时根据 URL 或 localStorage 设置 selected
  useEffect(() => {
    const path = window.location.pathname;
    const found = menuRouteMap.find(item => item.path === path);
    if (found) {
      setSelected(found.key);
      localStorage.setItem('selectedMenu', String(found.key));
    } else {
      const stored = localStorage.getItem('selectedMenu');
      const parsed = stored !== null ? Number(stored) : NaN;
      if (typeof parsed === 'number' && !isNaN(parsed)) {
        setSelected(parsed);
      } else {
        setSelected(0);
      }
    }
  // eslint-disable-next-line
  }, []);

  // 切换菜单时，setSelected 并写入 localStorage，并同步 URL
  const handleMenuSelect = (val: number) => {
    setSelected(val);
    localStorage.setItem('selectedMenu', String(val));
    const route = menuRouteMap.find(item => item.key === val);
    if (route) {
      window.history.pushState({}, '', route.path);
    }
  };

  return (
    <Container fluid className="aipodcast-root-container p-0">
      {/* 顶部导航栏 */}
      <Navbar bg="light" variant="light" expand="lg" className="aipodcast-topbar">
        <Navbar.Brand className="aipodcast-navbar-brand">
          <span className="aipodcast-navbar-icon" role="img" aria-label="播客">🎙️</span>
          { process.env.REACT_APP_TITLE || 'AI播客' }
        </Navbar.Brand>
      </Navbar>
      {/* 主体区域 */}
      <Row className="aipodcast-mainrow g-0">
        {/* 侧边栏 */}
        <Col xs={2} md={2} className="aipodcast-sidebar bg-light border-end" style={{ minWidth: 200, maxWidth: 240 }}>
          <Nav className="flex-column py-4">
            <Nav.Link
              as="button"
              className={`aipodcast-sidebar-item mb-2${selected === 0 ? ' active' : ''}`}
              onClick={() => handleMenuSelect(0)}
            >
              播客生成
            </Nav.Link>
            <Nav.Link
              as="button"
              className={`aipodcast-sidebar-item mb-2${selected === 1 ? ' active' : ''}`}
              onClick={() => handleMenuSelect(1)}
            >
              历史记录
            </Nav.Link>
            <Nav.Link
              as="button"
              className={`aipodcast-sidebar-item${selected === 2 ? ' active' : ''}`}
              onClick={() => handleMenuSelect(2)}
            >
              TTS生成
            </Nav.Link>
          </Nav>
        </Col>
        {/* 主内容区 */}
        <Col xs={10} md={10} className="aipodcast-maincontent">
          {selected === 0 ? (
            <PodcastGenerator />
          ) : selected === 1 ? (
            <History />
          ) : selected === 2 ? (
            <TTSGenerator />
          ) : (
            <div style={{ fontSize: 20, color: '#888' }}>请选择菜单</div>
          )}
        </Col>
      </Row>
    </Container>
  );
};

export default App;