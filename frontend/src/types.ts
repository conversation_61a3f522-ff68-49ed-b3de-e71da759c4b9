// 对话项类型
export interface DialogueItem {
  role: string;
  text: string;
}

// 文本内容类型
export interface TextContent {
  title: string;
  labels: {
    articleInput: string;
    generateScript: string;
    jsonInput: string;
    submit: string;
    loadExample: string;
  };
  placeholders: {
    jsonInput: string;
  };
}

// 角色声音映射类型
export interface VoiceRoleItem {
  key: string;
  desc: string;
}

// API响应类型
export interface ApiResponse {
  status: string;
  audio: string;
}

export interface ApiErrorResponse {
  error: string;
  status: string;
}