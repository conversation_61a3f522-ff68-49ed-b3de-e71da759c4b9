import React, { useState, useRef, useEffect } from 'react';
import { Row, Col, <PERSON>, But<PERSON>, Spinner, Alert } from 'react-bootstrap';
import { generateAudio, generateScript } from './services/api';
import { DialogueItem, TextContent, VoiceRoleItem } from './types';
import { getVoiceRoles } from './services/api';
type VoiceRole = { role: string; voices: VoiceRoleItem[] };

const textContent: TextContent = {
  title: "播客生成器",
  labels: {
    articleInput: "输入原始文章",
    generateScript: "生成播客脚本",
    jsonInput: "播客脚本",
    submit: "生成播客语音",
    loadExample: "加载示例"
  },
  placeholders: {
    jsonInput: "对话轮流进行, 每轮最多50秒。文本越自然, 生成的音频效果越好。"
  }
};

const exampleJsonDialogue = `[
    {
    "role": "0",
    "text": "欢迎来到开练成长社区。今天我们聊什么呢？"
  },
  {
    "role": "1",
    "text": "嗯，我们要聊教育培训机构在暑假招生季的一些高效营销策略。"
  },
  {
    "role": "0",
    "text": "听起来很有意思啊。那具体有哪些策略呢。"
  },
  {
    "role": "1",
    "text": "其实主要有四种，就是秒杀，拼团，拆礼盒，还有抽奖。"
  } 
]`;

function PodcastGenerator() {
  const [articleText, setArticleText] = useState<string>('');
  const [jsonDialogue, setJsonDialogue] = useState<string>('');
  const [audioUrl, setAudioUrl] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isGeneratingScript, setIsGeneratingScript] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  const [voiceRoles, setVoiceRoles] = useState<{ [lang: string]: VoiceRole[] }>({});
  const [selectedVoices, setSelectedVoices] = useState<{ [lang: string]: { [roleId: string]: string } }>({});
  const [currentLang] = useState<string>('zh');

  useEffect(() => {
    getVoiceRoles()
      .then((rolesObj: { zh: VoiceRole[]; en?: VoiceRole[] }) => {
        setVoiceRoles(rolesObj);
        const voicesByLang: { [lang: string]: { [roleId: string]: string } } = {};
        Object.entries(rolesObj).forEach(([lang, roles]) => {
          voicesByLang[lang] = {};
          (roles as VoiceRole[]).forEach((role) => {
            voicesByLang[lang][role.role] = role.voices && role.voices.length > 0 ? role.voices[0].key : '';
          });
        });
        setSelectedVoices(voicesByLang);
      })
      .catch(() => setError('获取角色列表失败'));
  }, []);

  const audioRef = useRef<HTMLAudioElement>(null);

  const loadExample = () => {
    setJsonDialogue(exampleJsonDialogue);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);
    setAudioUrl(null);
    setIsLoading(true);

    try {
      if (!jsonDialogue) {
        throw new Error('对话JSON是必填的');
      }
      let dialogueData: DialogueItem[];
      try {
        dialogueData = JSON.parse(jsonDialogue);
        if (!Array.isArray(dialogueData)) {
          throw new Error('JSON必须是一个数组');
        }
      } catch (jsonError) {
        throw new Error('无效的JSON格式');
      }
      // 明确 response 类型
      const response: {
        audio_url?: string;
        audio?: string;
        [key: string]: any;
      } = await generateAudio(
        jsonDialogue,
        currentLang,
        // 组装 voiceMap: VoiceRoleItem[]，只包含当前选中的 key/desc
        Object.entries(selectedVoices?.[currentLang] || {}).map(([role, key]) => {
          // 从 voiceRoles 查找 desc
          const desc =
            (voiceRoles[currentLang]?.find(r => r.role === role)?.voices.find(v => v.key === key)?.desc) || '';
          return { key, desc };
        })
      );
      // 优化音频播放逻辑
      let url: string | null = null;
      if (typeof response.audio_url === 'string' && response.audio_url.trim() !== '') {
        url = response.audio_url.trim();
      } else if (typeof response.audio === 'string' && response.audio.trim() !== '') {
        const audioBlob = base64ToBlob(response.audio, 'audio/wav');
        url = URL.createObjectURL(audioBlob);
      }

      if (url) {
        setAudioUrl(url);
        setTimeout(() => {
          if (audioRef.current) {
            audioRef.current.load();
            audioRef.current.play();
          }
        }, 0);
      } else {
        throw new Error('未获取到有效的音频数据');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : String(err));
    } finally {
      setIsLoading(false);
    }
  };

  const handleGenerateScript = async () => {
    setError(null);
    setIsGeneratingScript(true);

    try {
      if (!articleText) {
        throw new Error('需要输入文章内容');
      }
      const response = await generateScript(articleText, 'zh');
      setJsonDialogue(JSON.stringify(response.script, null, 2));
    } catch (err) {
      setError(err instanceof Error ? err.message : String(err));
    } finally {
      setIsGeneratingScript(false);
    }
  };

  const base64ToBlob = (base64: string, mimeType: string): Blob => {
    const byteCharacters = atob(base64);
    const byteArrays = [];
    for (let offset = 0; offset < byteCharacters.length; offset += 512) {
      const slice = byteCharacters.slice(offset, offset + 512);
      const byteNumbers = new Array(slice.length);
      for (let i = 0; i < slice.length; i++) {
        byteNumbers[i] = slice.charCodeAt(i);
      }
      const byteArray = new Uint8Array(byteNumbers);
      byteArrays.push(byteArray);
    }
    return new Blob(byteArrays, { type: mimeType });
  };

  useEffect(() => {
    return () => {
      if (audioUrl) {
        URL.revokeObjectURL(audioUrl);
      }
    };
  }, [audioUrl]);

  const text = textContent;

  return (
    <div className="aipodcast-card">
      {/* <div className="aipodcast-section-title mb-4">播客生成器</div> */}
      {error && (
        <Alert variant="danger" className="mb-3">
          {error}
        </Alert>
      )}
      <Form onSubmit={handleSubmit}>
        <Row className="g-4 flex-lg-nowrap">
          <Col xs={12} lg={6} className="d-flex flex-column">
            <div>
              <div className="aipodcast-form-label">{text.labels.articleInput}</div>
              <Form.Group>
                <Form.Control
                  as="textarea"
                  rows={10}
                  value={articleText}
                  onChange={(e) => setArticleText(e.target.value)}
                  placeholder="在此粘贴文章内容以生成播客剧本"
                  style={{ borderRadius: '0.7rem', border: '1.5px solid #e3e3e3' }}
                />
              </Form.Group>
              <div className="d-flex justify-content-end mt-3">
                <Button
                  className="aipodcast-btn-primary"
                  onClick={handleGenerateScript}
                  disabled={isGeneratingScript || !articleText}
                >
                  {isGeneratingScript ? (
                    <>
                      <Spinner as="span" animation="border" size="sm" className="me-2" />
                      生成中...
                    </>
                  ) : (
                    text.labels.generateScript
                  )}
                </Button>
              </div>
            </div>
          </Col>
          <Col xs={12} lg={6} className="d-flex flex-column">
            <div>
              <div className="d-flex align-items-center justify-content-between">
                <div className="aipodcast-form-label">{text.labels.jsonInput}</div>
                <Button
                  variant="outline-secondary"
                  onClick={loadExample}
                  size="sm"
                  style={{ fontWeight: 600, borderRadius: '0.5rem' }}
                >
                  {text.labels.loadExample}
                </Button>
              </div>
              <Form.Group>
                <Form.Control
                  as="textarea"
                  rows={10}
                  value={jsonDialogue}
                  onChange={(e) => setJsonDialogue(e.target.value)}
                  placeholder={text.placeholders.jsonInput}
                  style={{ borderRadius: '0.7rem', border: '1.5px solid #e3e3e3' }}
                />
              </Form.Group>
            </div>
          </Col>
        </Row>
        <Row className="g-4 mt-4 align-items-stretch">
          {Array.isArray(voiceRoles[currentLang]) && voiceRoles[currentLang].length > 0 && (
            <Col xs={12}>
              <div style={{ background: '#f8fbff', borderRadius: '0.8rem', padding: '1.2rem 1rem', marginBottom: '1rem', boxShadow: '0 1.5px 8px 0 rgba(0,123,255,0.04)' }}>
                <div className="aipodcast-form-label mb-2">角色声音选择</div>
                <div className="role-voice-row">
                  {voiceRoles[currentLang].map((role) => {
                    const voiceOptions: VoiceRoleItem[] = Array.isArray(role.voices) ? role.voices : [];
                    return (
                      <div className="role-voice-col" key={role.role}>
                        <div className="role-desc">角色 {role.role}</div>
                        <Form.Select
                          value={selectedVoices?.[currentLang]?.[role.role] || ''}
                          onChange={e => {
                            setSelectedVoices(prev => ({
                              ...prev,
                              [currentLang]: {
                                ...(prev[currentLang] || {}),
                                [role.role]: e.target.value
                              }
                            }));
                          }}
                          style={{ borderRadius: '0.5rem', border: '1.5px solid #e3e3e3' }}
                        >
                          {voiceOptions.map(v => (
                            <option key={v.key} value={v.key}>
                              {v.desc}
                            </option>
                          ))}
                        </Form.Select>
                      </div>
                    );
                  })}
                </div>
              </div>
            </Col>
          )}
        </Row>
        <Row className="g-4 mt-4 align-items-stretch">
          <Col xs={12}>
            <div style={{ background: '#f8fbff', borderRadius: '0.8rem', padding: '1.2rem 1rem', boxShadow: '0 1.5px 8px 0 rgba(0,123,255,0.04)' }}>
              <Row className="align-items-center flex-column flex-md-row">
                <Col xs={12} md={5} className="mb-3 mb-md-0">
                  <Button
                    type="submit"
                    className="w-100 aipodcast-btn-primary"
                    size="lg"
                    disabled={isLoading}
                  >
                    {isLoading ? (
                      <>
                        <Spinner as="span" animation="border" size="sm" className="me-2" />
                        处理中...
                      </>
                    ) : (
                      text.labels.submit
                    )}
                  </Button>
                </Col>
                <Col xs={12} md={7}>
                  <div className="audio-placeholder-center">
                    {audioUrl ? (
                      <audio ref={audioRef} controls className="w-100" style={{ maxWidth: 420 }}>
                        <source src={audioUrl} type="audio/wav" />
                        您的浏览器不支持音频元素。
                      </audio>
                    ) : (
                      <p className="text-muted mb-0">
                        生成的音频将显示在这里。
                      </p>
                    )}
                  </div>
                </Col>
              </Row>
            </div>
          </Col>
        </Row>
      </Form>
    </div>
  );
}

export default PodcastGenerator;