from datetime import datetime

from sqlalchemy import Column, Integer, String, Text, DateTime, ForeignKey
from sqlalchemy.orm import declarative_base, relationship

Base = declarative_base()

class PodcastScript(Base):
    __tablename__ = "podcast_script"

    id = Column(Integer, primary_key=True, autoincrement=True)
    original_text = Column(Text, nullable=False)
    summary_text = Column(Text, nullable=True)
    script_text = Column(Text, nullable=False)
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)

    def __repr__(self):
        return f"<PodcastScript(id={self.id})>"

class PodcastAudio(Base):
    __tablename__ = "podcast_audio"

    id = Column(Integer, primary_key=True, autoincrement=True)
    script_text = Column(Text, nullable=False)
    voice_model_name = Column(String(128), nullable=False)
    audio_url = Column(String(512), nullable=False)
    edit_audio_url = Column(String(512), nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)

    def __repr__(self):
        return (
            f"<PodcastAudio(id={self.id}, "
            f"script_text={self.script_text!r}, "
            f"voice_model_name={self.voice_model_name!r}, "
            f"audio_url={self.audio_url!r}, "
            f"edit_audio_url={self.edit_audio_url!r}, "
            f"created_at={self.created_at})>"
        )
class TTSRecord(Base):
    __tablename__ = "tts_record"

    id = Column(Integer, primary_key=True, autoincrement=True)
    text = Column(Text, nullable=False)
    voice = Column(String(128), nullable=False)
    audio_url = Column(String(512), nullable=False)
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)

    def __repr__(self):
        return (
            f"<TTSRecord(id={self.id}, text={self.text!r}, voice={self.voice!r}, "
            f"audio_url={self.audio_url!r}, created_at={self.created_at})>"
        )