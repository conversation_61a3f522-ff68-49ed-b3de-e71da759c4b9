# API配置
[api]
PORT = 5008
HOST = "0.0.0.0"
DEBUG = true

# CORS配置
[cors]
CORS_ORIGINS = "http://localhost:3008,https://your-frontend-domain.com"

# AI 模型配置
[ai]
LLM_API_KEY = "sk-pqkelnO3f7F5eW8_yj5OpGk_b-Wn73AxYRqM00uqMAZd97U2k0YDWO-r2OM"
LLM_MODEL_NAME = "deepseek-v3"
LLM_BASE_URL = "https://oneapi.01sworld.top:8443/v1"

# Azure TTS 配置
[azure_tts]
AZURE_TTS_KEY = "E9wHI5q3mSI9RRsCo9OmAEQmKFoEMVCdFYaXdBTqqBUpiaKzZlirJQQJ99ALACHYHv6XJ3w3AAAAACOGhYsJ"
AZURE_TTS_REGION = "eastus2"

# Postgres 数据库配置
[postgres]
host = "localhost"
port = 5432
user = "user"
password = "pass123"
database = "aipodcast"

# MinIO S3 配置
[minio]
endpoint = "localhost:9000"
access_key = "378Hl1aK7awMADSXiYrv"
secret_key = "q7iXVbOJaZ0W243plzkpQIBrbCsni7hmxB5NluSy"
bucket = "aipodcast"
region = ""  # 可选

# VOICE_MAP 配置
# voiceMap 结构已统一为对象数组，每项包含 key（语音标识）和 desc（描述）。
# 例如：0 = [{key="zh-CN-xxx", desc="主讲人1"}, ...]
[voice_map.zh]
0 = [{key="zh-CN-Xiaoxiao2:DragonHDFlashLatestNeural", desc="主讲人1"}, {key="zh-CN-Xiaochen:DragonHDFlashLatestNeural", desc="主讲人2"}, {key="zh-CN-Xiaoxiao:DragonHDFlashLatestNeural", desc="主讲人3"}, {key="zh-CN-Xiaochen:DragonHDLatestNeural", desc="主讲人4"}]
1 = [{key="zh-CN-Yunyi:DragonHDFlashLatestNeural", desc="主讲人1"}, {key="zh-CN-Yunxiao:DragonHDFlashLatestNeural", desc="主讲人2"}, {key="zh-CN-YunyangNeural", desc="主讲人3"}]

[voice_map.en]
0 = [{key="en-US-JennyNeural", desc="Speaker1"}, {key="en-US-AriaNeural", desc="Speaker2"}]
1 = [{key="en-US-GuyNeural", desc="Speaker1"}, {key="en-US-DavisNeural", desc="Speaker2"}]