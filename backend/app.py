import sys
import os
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from flask import Flask, request, jsonify
from flask_cors import CORS
from backend.config import config
from backend.utils.parse_utils import parse_bool

from backend.services.podcast_audio import generate_audio_service
from backend.services.podcast_script import generate_script_service
from backend.config import get_voice_map

# 导入数据库模型和 session 工厂
from backend.db.models import PodcastScript, PodcastAudio
from backend.db.db import get_engine_and_sessionmaker

# 初始化Flask应用
app = Flask(__name__)
CORS(app, resources={r"/api/*": {"origins": config.get('CORS_ORIGINS', '*').split(',')}})

# 错误处理中间件
@app.errorhandler(Exception)
def handle_exception(e):
    # 记录错误
    app.logger.error(f"Unhandled exception: {str(e)}")
    
    # 返回友好的错误消息
    return jsonify({
        "error": str(e),
        "status": "error"
    }), 500

# 健康检查接口
@app.route('/api/health', methods=['GET'])
def health_check():
    return jsonify({"status": "ok"})

# 获取所有可用 voice 角色信息
@app.route('/api/podcast/voice_roles', methods=['GET'])
def get_voice_roles():
    """
    返回所有可用 voice 角色信息，按语言分组。
    支持 GET 查询参数 lang，如 /api/podcast/voice_roles?lang=zh。
    如果传入 lang，则只返回该语言的角色数据，否则返回所有语言。
    """
    lang = request.args.get("lang")
    voice_map = get_voice_map()
    if lang and lang in voice_map:
        roles = voice_map[lang]
        result = {
            lang: [
                {"role": role, "voices": voice}
                for role, voice in roles.items()
            ]
        }
    else:
        result = {}
        for l, roles in voice_map.items():
            result[l] = [
                {"role": role, "voices": voice}
                for role, voice in roles.items()
            ]
    return jsonify(result)

# TTS 可用voice类型列表接口
@app.route('/api/tts/voices', methods=['GET'])
def tts_voices():
    from backend.config import get_voice_map
    # voice_map 结构已统一为对象数组，每项含 key/desc
    voice_map = get_voice_map()
    # 获取查询参数 lang，未指定则默认 zh
    lang = request.args.get("lang", "zh")
    voices = []
    if lang in voice_map:
        lang_roles = voice_map[lang]
        for v in lang_roles.values():
            if isinstance(v, list):
                voices.extend(v)
            else:
                voices.append(v)
    else:
        voices = []
    # voices 为对象数组 [{key, desc}]
    voice_objs = []
    for v in voices:
        if isinstance(v, dict):
            voice_objs.append({"key": v.get("key", ""), "desc": v.get("desc", "")})
        else:
            voice_objs.append({"key": v, "desc": ""})
    return jsonify({
        "status": "success",
        "voices": voice_objs,
        "lang": lang
    })

@app.route('/api/tts/history', methods=['GET'])
def tts_history():
    from backend.db.models import TTSRecord
    _, SessionLocal = get_engine_and_sessionmaker()
    session = SessionLocal()
    try:
        # 分页参数
        try:
            page = int(request.args.get('page', 1))
            page_size = int(request.args.get('page_size', 10))
        except Exception:
            return jsonify({"status": "error", "error": "分页参数格式错误"}), 400
        if page < 1 or page_size < 1 or page_size > 100:
            return jsonify({"status": "error", "error": "分页参数不合法"}), 400

        query = session.query(TTSRecord).order_by(TTSRecord.id.desc())
        total = query.count()
        records = query.offset((page - 1) * page_size).limit(page_size).all()
        result = [
            {
                "id": r.id,
                "text": r.text,
                "voice": r.voice,
                "audio_url": r.audio_url,
                "created_at": r.created_at.isoformat() if r.created_at else None
            }
            for r in records
        ]
        return jsonify({
            "status": "success",
            "total": total,
            "page": page,
            "page_size": page_size,
            "records": result
        })
    finally:
        session.close()

@app.route('/api/tts/generate', methods=['POST'])
def tts_generate():
    from backend.services.tts_service import generate_tts_audio

    data = request.get_json()
    result, status = generate_tts_audio(data)
    return jsonify(result), status

# PodcastScript 历史记录查询接口
@app.route('/api/podcast/history/scripts', methods=['GET'])
def get_scripts_history():
    _, SessionLocal = get_engine_and_sessionmaker()
    session = SessionLocal()
    try:
        scripts = session.query(PodcastScript).order_by(PodcastScript.id.desc()).all()
        result = [
            {
                "id": s.id,
                "original_text": s.original_text,
                "summary_text": s.summary_text,
                "script_text": s.script_text,
                "created_at": s.created_at.isoformat() if s.created_at else None
            }
            for s in scripts
        ]
        return jsonify(result)
    finally:
        session.close()

# PodcastAudio 历史记录查询接口
@app.route('/api/podcast/history/audios', methods=['GET'])
def get_audios_history():
    _, SessionLocal = get_engine_and_sessionmaker()
    session = SessionLocal()
    try:
        audios = session.query(PodcastAudio).order_by(PodcastAudio.id.desc()).all()
        result = [
            {
                "id": a.id,
                "script_text": a.script_text,
                "voice_model_name": a.voice_model_name,
                "audio_url": a.audio_url,
                "edit_audio_url": a.edit_audio_url,
                "created_at": a.created_at.isoformat() if a.created_at else None
            }
            for a in audios
        ]
        return jsonify(result)
    finally:
        session.close()

# 播客脚本生成接口
@app.route('/api/podcast/generate/script', methods=['POST'])
def generate_script_api():
    data = request.get_json()
    input_text = data.get('input_text', '')
    language = data.get('language', 'zh')
    result = generate_script_service(input_text, language)
    return jsonify(result)

# 音频生成接口
@app.route('/api/podcast/generate/audio', methods=['POST'])
def generate_audio():
    import json
    data = request.get_json()
    json_dialogue_str = data.get('json_dialogue', '[]')
    language = data.get('language', 'zh')

    voice_map = data.get('voice_map')
    if isinstance(voice_map, str):
        try:
            voice_map = json.loads(voice_map)
        except Exception as e:
            return jsonify({"error": f"voice_map 字符串解析失败: {str(e)}", "status": "error"}), 400
    elif not (isinstance(voice_map, dict) or voice_map is None):
        return jsonify({"error": "voice_map 必须为 dict 或 JSON 字符串", "status": "error"}), 400

    generate_edit_audio = parse_bool(data.get('generate_edit_audio'), False)
    audio_edit_preset = data.get('audio_edit_preset', None)

    result = generate_audio_service(
        json_dialogue_str,
        language,
        voice_map=voice_map,
        generate_edit_audio=generate_edit_audio,
        audio_edit_preset=audio_edit_preset
    )
    # 若 result 中包含 role/voices 字段且为字符串数组，需转为对象数组
    def convert_voices(obj):
        if isinstance(obj, dict):
            for k, v in obj.items():
                if k == "voices" and isinstance(v, list):
                    # 检查是否为字符串数组
                    if v and isinstance(v[0], str):
                        obj[k] = [{"key": vv, "desc": ""} for vv in v]
                    elif v and isinstance(v[0], dict):
                        obj[k] = [{"key": vv.get("key", ""), "desc": vv.get("desc", "")} for vv in v]
                else:
                    convert_voices(v)
        elif isinstance(obj, list):
            for item in obj:
                convert_voices(item)
        return obj
    
    result = convert_voices(result)
    return jsonify(result)

# 音频编辑接口
@app.route('/api/podcast/edit/audio', methods=['POST'])
def edit_audio():
    from backend.services.podcast_edit import edit_audio_by_id
    data = request.get_json()
    _, SessionLocal = get_engine_and_sessionmaker()
    session = SessionLocal()
    try:
        audio_id = data.get("id")
        audio_edit_preset = data.get("audio_edit_preset", "default")

        # 参数校验
        if not audio_id:
            return jsonify({"success": False, "status": "param_error", "error": "缺少 id 参数"}), 400
        try:
            audio_id = int(audio_id)
        except Exception:
            return jsonify({"success": False, "status": "param_error", "error": "id 必须为整数"}), 400

        result = edit_audio_by_id(session, audio_id, audio_edit_preset)
        return jsonify(result)
    finally:
        session.close()

# 启动应用
if __name__ == '__main__':
    from backend.db.db import init_db
    init_db()
    
    port = int(config.get('PORT', 5008))
    host = config.get('HOST', '0.0.0.0')
    debug = config.get('DEBUG', False)
    if isinstance(debug, str):
        debug = debug.lower() == 'true'
    
    app.run(host=host, port=port, debug=debug)