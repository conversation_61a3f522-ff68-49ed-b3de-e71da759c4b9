# AIPodcast：AI 智能播客生成平台

AIPodcast 是一个端到端的 AI 播客自动生成平台，集成了大语言模型（LLM）脚本生成、文本转语音（TTS）、多语言支持、本地/云端推理、可视化前端等功能，助力用户一键生成高质量播客内容。项目采用模块化架构，支持灵活扩展与容器化部署，适用于内容创作、教育、媒体等多场景。

---

## 功能特性

- **一键播客生成**：输入主题，自动生成播客脚本并合成音频
- **多语言支持**：支持中英文播客内容生成与语音合成
- **多种 TTS 引擎**：集成 Azure TTS、本地推理（BigVGAN、Vocos 等）
- **可视化前端**：基于 React 的交互式界面，支持历史记录、音频播放与下载
- **模块化后端**：FastAPI 提供 RESTful API，支持脚本生成、音频合成、MinIO 文件存储等
- **容器化部署**：支持 Docker Compose 一键部署，便于本地或云端运行
- **可扩展架构**：易于集成新模型、新 TTS 服务或第三方 API

---

## 快速开始

### 1. 克隆项目

```bash
git clone https://github.com/your-org/AIPodcast.git
cd AIPodcast
```

### 2. 使用 Docker Compose 部署（推荐）

确保已安装 [Docker](https://www.docker.com/) 与 [Docker Compose](https://docs.docker.com/compose/)。

```bash
docker-compose up --build
```

- 前端默认访问：http://localhost:3000
- 后端 API 默认：http://localhost:5008

### 3. 本地开发环境部署

#### 后端

```bash
cd backend
pip install -r requirements.txt

python3 app.py 
# 或者
flask run --host=0.0.0.0 --port=5008 --reload
```

#### 前端

```bash
cd frontend
yarn install
yarn start
```

#### 本地推理模块（可选）

```bash
cd local_inference
pip install -r requirements.txt
python main.py
```

---

## 使用方法

1. 访问前端页面，输入播客主题或脚本需求
2. 选择语言、TTS 引擎等参数
3. 点击生成，系统自动完成脚本生成与音频合成
4. 在历史记录中查看、播放或下载生成的播客音频

---

## 目录结构

```plaintext
AIPodcast/
├── backend/                # 后端服务（FastAPI，脚本生成、TTS、MinIO 等）
│   ├── app.py              # FastAPI 主入口
│   ├── config.py/.toml     # 配置文件
│   ├── db/                 # 数据库模型与操作
│   ├── llm/                # LLM 脚本生成模块
│   ├── podcast_script/     # 多语言脚本生成逻辑
│   ├── services/           # TTS、MinIO、脚本等服务
│   └── tests/              # 后端测试
├── frontend/               # 前端（React，TypeScript）
│   ├── public/             # 静态资源
│   ├── src/                # 前端源码
│   │   ├── services/       # API 封装
│   │   ├── App.tsx         # 主页面
│   │   └── ...             # 其他组件
│   └── Dockerfile          # 前端容器配置
├── local_inference/        # 本地推理与音频处理模块
│   ├── modules/            # 音频分词、合成、量化等子模块
│   ├── requirements.txt    # 依赖
│   └── main.py             # 本地推理入口
├── docker-compose.yml      # 一键部署编排
└── readme.md               # 项目说明文档
```

**主要目录说明：**

- `backend/`：后端 API 服务，负责脚本生成、TTS、文件存储等核心逻辑
- `frontend/`：React 前端，提供用户交互界面
- `local_inference/`：本地音频推理与处理，支持自定义模型
- `docker-compose.yml`：多服务一键部署入口

---

## 技术栈

- **前端**：React, TypeScript, Yarn
- **后端**：Python, FastAPI, MinIO, Azure TTS, Uvicorn
- **本地推理**：PyTorch, BigVGAN, Vocos, 量化/分词/合成模块
- **容器化**：Docker, Docker Compose
- **其他**：RESTful API, 多语言支持

---

## 数据流与架构亮点

1. **数据流**：前端输入 → 后端 API（脚本生成/音频合成）→ 本地推理/云服务 → MinIO 存储 → 前端展示/下载
2. **架构亮点**：
   - 模块化设计，便于扩展与维护
   - 支持本地与云端多种 TTS/LLM 方案
   - 多语言、多角色播客生成
   - 容器化部署，环境一致性强
   - 前后端分离，接口清晰

---

## 贡献指南

欢迎社区贡献！请参考以下流程：

1. Fork 本仓库并新建分支
2. 提交代码前请确保通过所有测试
3. 提交 Pull Request 并详细描述变更内容
4. 参与代码评审与讨论

如需贡献新模型、TTS 服务或前端功能，请先在 Issue 区讨论设计方案。

---

## 许可证

本项目采用 [MIT License](LICENSE)。

---

## 致谢与引用

- 感谢开源社区及相关项目（如 FastAPI、React、BigVGAN、Vocos、MinIO、Azure TTS 等）的支持与贡献。
- 如在学术或商业项目中使用本项目，请注明引用来源。

---

## 常见问题（FAQ）

**Q1：如何切换 TTS 引擎或添加新模型？**  
A：可在后端 `services/` 目录下扩展 TTS 服务，并在前端参数中选择。

**Q2：支持哪些语言？**  
A：目前支持中文、英文，后续可扩展更多语言。

**Q3：如何自定义播客脚本风格？**  
A：可修改 `backend/podcast_script/` 下的脚本生成逻辑，或集成自定义 LLM。

**Q4：如何部署到云服务器？**  
A：推荐使用 Docker Compose，或根据实际环境调整端口与配置。

---

## 联系方式

- 项目主页：https://github.com/your-org/AIPodcast
- 维护者邮箱：<EMAIL>
- Issue 反馈：https://github.com/your-org/AIPodcast/issues

---

AIPodcast —— 让 AI 赋能播客创作，开启智能音频新时代！
